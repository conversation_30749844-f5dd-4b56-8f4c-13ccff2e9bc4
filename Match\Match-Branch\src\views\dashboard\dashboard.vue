<template>
   <div class="dashboard-container">
    <div class="cards-grid">
      <div class="card" @click="navigateTo('/roulette')">
        <div class="card-icon">
          <svg viewBox="0 0 100 100" class="roulette-icon">
            <circle cx="50" cy="50" r="45" fill="#2c3e50" stroke="#ecf0f1" stroke-width="2"/>
            <circle cx="50" cy="50" r="35" fill="none" stroke="#ecf0f1" stroke-width="1"/>
            <path d="M50,15 L50,50 L85,50" fill="none" stroke="#e74c3c" stroke-width="2"/>
            <circle cx="50" cy="50" r="3" fill="#f39c12"/>
          </svg>
        </div>
        <h3>Lucky Roulette</h3>
      </div>

      <div class="card" @click="navigateTo('/race')">
        <div class="card-icon">
          <svg viewBox="0 0 100 100" class="race-icon">
            <rect x="10" y="40" width="80" height="20" rx="10" fill="#3498db"/>
            <circle cx="25" cy="65" r="8" fill="#2c3e50"/>
            <circle cx="75" cy="65" r="8" fill="#2c3e50"/>
            <rect x="20" y="25" width="60" height="15" rx="5" fill="#ecf0f1"/>
            <polygon points="15,40 25,30 35,40" fill="#e74c3c"/>
          </svg>
        </div>
        <h3>Racing Car</h3>
      </div>

      <div class="card" @click="navigateTo('/color')">
        <div class="card-icon">
          <svg viewBox="0 0 100 100" class="color-icon">
            <rect x="20" y="20" width="25" height="25" fill="#e74c3c" rx="3"/>
            <rect x="55" y="20" width="25" height="25" fill="#3498db" rx="3"/>
            <rect x="20" y="55" width="25" height="25" fill="#f39c12" rx="3"/>
            <rect x="55" y="55" width="25" height="25" fill="#27ae60" rx="3"/>
          </svg>
        </div>
        <h3>Lucky Color</h3>
      </div>

      <!-- <div class="card" @click="navigateTo('/printer')">
        <div class="card-icon">
          <svg viewBox="0 0 100 100" class="config-icon">
            <rect x="20" y="30" width="60" height="40" fill="#95a5a6" rx="5"/>
            <rect x="25" y="35" width="50" height="30" fill="#ecf0f1" rx="3"/>
            <circle cx="35" cy="75" r="3" fill="#2c3e50"/>
            <circle cx="65" cy="75" r="3" fill="#2c3e50"/>
            <rect x="30" y="15" width="40" height="20" fill="#34495e" rx="3"/>
          </svg>
        </div>
        <h3>Configuration</h3>
      </div> -->
    </div>
   </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped lang="scss">
.dashboard-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #4D5057;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 800px;
}

.card {
  background: linear-gradient(145deg, #2c3e50, #34495e);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    border-color: #3498db;
  }
}

.card-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  
  svg {
    width: 100%;
    height: 100%;
  }
}

h3 {
  color: #ecf0f1;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.roulette-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.race-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.color-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.config-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}
</style>

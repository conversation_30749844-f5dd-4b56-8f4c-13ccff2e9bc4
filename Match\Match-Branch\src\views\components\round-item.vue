<template>
    <div :class="['round-item']" @click="selectRound(item)">
        <div class="sn"> SN - 1000</div>
        <div class="time">
            Locked in
            < 1.33 min <!-- <span>{{ formatTime(item.endTime).hours }}</span> hour
                <span>{{ formatTime(item.endTime).minutes }}</span> min
                <span>{{ formatTime(item.endTime).seconds }}</span> second -->
        </div>
    </div>
</template>
<script lang="ts">

const props = defineProps({
    item: {
        type: Object,
        required: true
    }
});

const selectRound = () => {
    console.log(props.item);
}


</script>
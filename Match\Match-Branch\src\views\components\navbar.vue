<template>
    <div class="navbar">
        <!-- 退出按钮 -->
        <div class="nav-item" @click="handleExit">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round" />
                <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
            </svg>
        </div>

        <!-- 返回home选择入口 -->
        <div class="nav-item" @click="handleGoHome">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round" />
                <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
            </svg>
        </div>

        <!-- 筹码图标（无功能） -->
        <div class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
                <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="2" />
                <circle cx="12" cy="12" r="2" fill="currentColor" />
                <path d="M12 2v4M12 18v4M22 12h-4M6 12H2" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" />
            </svg>
        </div>

        <!-- 显示当前用户名 -->
        <div class="nav-item nav-username">
            <span class="username-text">{{ currentUsername }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from '@/store/global';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

const { profileModel, userLogout } = useGlobalStore();
const router = useRouter();

// 获取当前用户名
const currentUsername = computed(() => {
    return profileModel?.nickName || profileModel?.loginAccount || 'N/A';
});

// 退出功能
const handleExit = () => {
    // 这里可以添加退出确认对话框
    if (confirm('确定要退出吗？')) {
        // 调用store中的logout方法
        userLogout();
    }
};

// 返回home选择入口
const handleGoHome = () => {
    router.push('/home');
};
</script>

<style scoped>
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 100;
    border-radius: 5px;
    margin-bottom: 8px;
}

.nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-item:active {
    transform: translateY(0);
}

.nav-icon {
    width: 24px;
    height: 24px;
    color: white;
    stroke-width: 2;
}

.nav-username {
    cursor: default;
}

.nav-username:hover {
    background-color: transparent;
    transform: none;
}

.username-text {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar {
        padding: 10px 16px;
    }

    .nav-icon {
        width: 20px;
        height: 20px;
    }

    .username-text {
        font-size: 14px;
    }
}
</style>
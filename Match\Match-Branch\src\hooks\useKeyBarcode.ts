import { onMounted, onUnmounted } from 'vue'

let barcode = ''
let lastKeyTime = Date.now() // 初始化 lastKeyTime
const SCAN_THRESHOLD = 60 //间隔时间,判断是人为输入,还是码枪输入

export const useKeyBarcode = (cb: (arg0: string) => void) => {
    const keyDownEvent = (event: KeyboardEvent) => {
        const currentTime = Date.now()
        const timeDifference = currentTime - lastKeyTime

        if (event.key !== 'Enter' && event.key !== 'Shift') {
            if (timeDifference < SCAN_THRESHOLD) {//如果小于间隔时间,就是码枪输入,将字符串拼接
                barcode += event.key
            } else {
                barcode = event.key
            }
            lastKeyTime = currentTime // 更新 lastKeyTime
        }
        //打印的数据我放再下面
        // console.log('barcode', barcode, timeDifference, event.key)
        if (event.key === 'Enter') {
            //enter事件调用回调函数,将拼接完整的数据传出去,然后置为空字符串
            barcode.length > 10 && cb(barcode)
            barcode = ''
        }
    }
    onMounted(() => {
        document.addEventListener('keydown', keyDownEvent)
    })
    onUnmounted(() => {
        document.removeEventListener('keydown', keyDownEvent)
    })
}
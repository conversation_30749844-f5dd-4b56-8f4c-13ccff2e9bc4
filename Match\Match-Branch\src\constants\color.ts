import { EOrderState } from "@/typings/enum";

export interface EnumDescription {
    value: any;
    color: string;
    type: any;
    text?: string;
}

const COLOR_LIST: EnumDescription[] = [
    { type: EOrderState, value: EOrderState.Submitted, color: "#000", text: "Submitted", },
    { type: EOrderState, value: EOrderState.Settled, color: "#2CF886", text: "Settled", },
    { type: EOrderState, value: EOrderState.UnSettled, color: "#EAB3FE", text: "UnSettled", },
    { type: EOrderState, value: EOrderState.Lose, color: "#999", text: "Lose", },
    { type: EOrderState, value: EOrderState.Canceled, color: "#ba2106", text: "Canceled", },

];

export default COLOR_LIST;
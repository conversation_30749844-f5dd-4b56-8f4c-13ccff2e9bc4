import http from '@/utils/http';
import { AccountModel, ProfileModel, Result, UserAssetsModel } from './typings';


export default class UserService {

    public static login(data: {
        loginAccount: string,
        loginPassword: string
    }): Promise<Result<ProfileModel>> {
        return http.post("/passport/login", data);
    }


    public static getAssets(): Promise<Result<UserAssetsModel>> {
        return http.get("/user/assets");
    }

    public static cashiers() {
        return http.get<any, Result<Array<AccountModel>>>("/user/cashiers");
    }
}
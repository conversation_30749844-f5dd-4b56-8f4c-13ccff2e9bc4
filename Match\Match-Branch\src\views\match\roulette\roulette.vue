<template>
    <div class="page-body flex-row">
        <!-- 新增round选择组件 -->
        <div class="left-panel">
            <!-- <navbar></navbar> -->
            <round></round>
            <ticket :matchId="matchId" :items="state.items" @clear="clearSelections"
                :selected-options="selectedOptions"></ticket>
        </div>
        <div class="right-panel">
            <div class="flex-row container">
                <div class="box number-box">
                    <button class="button number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(0, 12)">
                        {{ item.optionName }}
                    </button>

                    <button class="button dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(0, 1)">
                        <span>1st dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box number-box">
                    <button class="button number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(12, 24)">
                        {{ item.optionName }}
                    </button>
                    <button class="button dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(1, 2)">
                        <span>2nd dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box number-box">
                    <button class="button number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(24, 36)">
                        {{ item.optionName }}
                    </button>
                    <button class="button dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(2, 3)">
                        <span>3rd dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
            <div class="flex-row container">
                <div class="box number-box">
                    <button class="button number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName == '0')">
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box sector-box">
                    <button class="button sector" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Sector')">
                        <span>Sector</span>
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
            <div class="flex-row container">
                <div class="box other-box">
                    <button class="button other" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOptions.includes(item) }]"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))">
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
            <div class="container">
                <div class="box selected-box">
                    <h3>Selected Options <button class="button red clear pull-right" @click="clearSelections">Clear
                            Selection</button></h3>
                    <div class="options">
                        <button class="button other" :data-tooltip="'x' + item.odds"
                            :class="[item.style, { 'selected': selectedOptions.includes(item) }]"
                            @click="onOptionClick(item)" v-for="(item, key) in selectedOptions">
                            {{ item.optionName }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import matchOptions from './option';

import Ticket from '../../components/ticket.vue';
import Round from '../../components/round.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';

import { EMatch } from '@/typings/enum';
import { MatchOption, StakeOrder } from '@/typings/typings';




const matchId = ref(EMatch.LuckyRoulette);

const [state] = useResettableReactive<StakeOrder>({
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const selectedOptions = ref<Array<MatchOption>>([]);


const onOptionClick = (e: MatchOption) => {

    if (selectedOptions.value.includes(e)) {
        selectedOptions.value.splice(selectedOptions.value.indexOf(e), 1);
    } else {
        selectedOptions.value.push(e);
    }
};
const clearSelections = () => {
    selectedOptions.value = [];
}



</script>

<style scoped lang="scss">
.container {
    gap: 5px;
    margin-bottom: 5px;

    [data-tooltip]:before {
        font-size: 26px;
    }



    .number-box {
        gap: 10px 15px;
        display: flex;
        flex-wrap: wrap;
        width: 33.333%;
        flex: 1;
        justify-content: center;
        align-items: center;

        .button {
            flex: 0 0 calc(33.333% - 13px); // 每行3个按钮，减去gap
            min-height: 60px

        }
    }

    .sector-box {
        .button {
            flex: 0 0 calc(16.6665% - 13px); // 每行3个按钮，减去gap
        }
    }

    .other-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        gap: 8px 15px;

        .button {
            flex: 0 0 calc(16.6665% - 13px); // 每行3个按钮，减去gap
        }
    }

    .selected-box {
        position: relative;
    }

    .selected-box .options {
        gap: 5px 5px;
        flex-wrap: wrap;
        min-height: 50px;
        // overflow-y: auto;
    }

    .sector-box {
        display: flex;
        width: 66.66666%;
        gap: 8px 15px;
        // justify-content: center;
        align-items: center;

    }

    .clear {
        width: 120px;
        height: 30px;
        font-size: 14px;
        position: absolute;
        right: 5px;
        top: 2px;
        z-index: 999;
    }
}
</style>

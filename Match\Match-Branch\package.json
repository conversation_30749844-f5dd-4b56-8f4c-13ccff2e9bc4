{"name": "match-branch", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build2": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.7", "decimal.js": "^10.4.3", "element-plus": "^2.8.8", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "numeral": "^2.0.6", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.3", "vue": "^3.5.12", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^22.9.3", "@types/numeral": "^2.0.5", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.1.4", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "sass-embedded": "^1.81.0", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.10", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.8"}}
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path';
import legacy from '@vitejs/plugin-legacy';

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';


import postcss from 'postcss';
import pxToViewport from 'postcss-px-to-viewport';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    createSvgIconsPlugin({
      // 指定目录(svg存放目录）
      iconDirs: [resolve(process.cwd(), 'src/assets/icon')],
      // 使用 svg 图标的格式（name为图片名称）
      symbolId: 'icon-[name]',
      //生成组件插入位置 只有两个值 body-last | body-first
      inject: 'body-last'
    }),
    AutoImport({
      imports: ['vue', 'vue-router'],
      resolvers: [ElementPlusResolver({
        importStyle: 'sass'
      })],
      dts: 'typings/auto-import.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver({
        importStyle: 'sass',
        directives: true
      })],
      dts: 'typings/components.d.ts'
    }),
    legacy({
      targets: ["defaults", "not IE 11", 'chromeAndroid>=52, iOS>=13.1'],
    })
  ],
  base: '/branch/',
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler',
        additionalData: `@use "@/assets/style/element/index.scss" as *;`,
      }
    },
    postcss: {
      plugins: [
        pxToViewport({
          unitToConvert: 'px', // 需要转换的单位，默认为'px'
          viewportWidth: 750,  // 基准视口宽度（对应设计稿的宽度）
          // viewportHeight: 667, // 视口高度（可选）
          unitPrecision: 5,    // px转换为vw的小数位数（很多情况下用5）
          propList: ['*'],     // 指定需要转换的属性，'*'表示全部转换，'font-size'表示只转换font-size属性
          viewportUnit: 'vmin',  // 指定需要转换成的视口单位，默认vw
          fontViewportUnit: 'vmin', // 字体使用的视口单位，默认vw
          selectorBlackList: [], // 指定不转换为视口单位的类名，用逗号隔开
          minPixelValue: 2,    // 小于或等于1px不转换为视口单位，默认1
          mediaQuery: false,   // 允许在媒体查询中转换px
          replace: true,       // 是否直接替换而不是添加后缀
          exclude: /node_modules/ // 设置忽略文件，用正则表达式匹配路径
        }),
      ]
    }
  },
  server: {
    host: '0.0.0.0',
    port: 8088,
    open: true
  },
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src')
      }
    ],
    extensions: ['.vue', '.ts', '.js', '.json']
  },
  build: {
    assetsDir: 'public',
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks(id) {
          return id.includes('node_modules') ? 'vender' : 'app';
          if (id.includes("node_modules")) {
            // 让每个插件都打包成独立的文件
            return id.toString().split("node_modules/")[1].split("/")[0].toString();
          }
        }
      }
    }
  }
})


import moment from 'moment';
import numeral from 'numeral';


export function formatNumber(value: any, format: string = '0,0'): string {
    return numeral(value).format(format);
}

export function formatDate(value: any, format: string = 'DD-MM-YYYY HH:mm'): string {
    return moment(value).format(format);
}

export function getRandomNumbers(count: number = 20): number[] {
    if (count < 0 || count > 80) {
        throw new Error("Count must be between 0 and 80");
    }

    // 创建1到80的数组
    const numbers = Array.from({ length: 80 }, (_, i) => i + 1);

    // Fisher-Yates洗牌算法
    for (let i = numbers.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
    }

    // 返回前count个元素
    return numbers.slice(0, count);
}
import { createApp } from 'vue';
// import './assets/style/element/index.scss'
import '@/assets/style/app.scss';
import 'element-plus/dist/index.css'
import App from './App.vue';

import router from "./router";

import 'virtual:svg-icons-register'

import i18n from '@/i18n';
import store from '@/store';

import VueNumeralFormat from '@/extension/numeral';
import VueMoment from '@/extension/moment';
import VueEnum from '@/extension/enum';



const app = createApp(App);

app.use(router)
    .use(store)
    .use(i18n)
    .use(VueNumeralFormat)
    .use(VueMoment)
    .use(VueEnum)
    .mount('#app');
<template>
    <div class="page-body flex-col">
        <div class="box">
            <el-form :model="formData" class="search-form" ref="searchForm" @submit.native.prevent="search">
                <el-form-item>
                    <el-input v-model="formData.orderNO" placeholder="Order No" clearable />
                </el-form-item>
                <el-form-item>
                    <el-select v-model="formData.matchId" placeholder="Game Select" clearable>
                        <el-option label="Lucky Roulette" :value="1000"></el-option>
                        <el-option label="Virtual Race" :value="2000"></el-option>
                        <el-option label="Lucky Color" :value="3000"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="startDate">
                    <el-date-picker type="date" placeholder="Start Date" format="YYYY-MM-DD" clearable
                        value-format="YYYY-MM-DD" v-model="formData.startDate"
                        :disabled-date="disableStartDate"></el-date-picker>
                </el-form-item>
                <el-form-item prop="endDate">
                    <el-date-picker type="date" placeholder="End Date" format="YYYY-MM-DD" clearable
                        value-format="YYYY-MM-DD" v-model="formData.endDate"
                        :disabled-date="disableEndDate"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select placeholder="Quick Date" v-model="formData.dateType" @change="quickDate">
                        <el-option label="Yesterday" :value="0"></el-option>
                        <el-option label="Today" :value="1"></el-option>
                        <el-option label="Last 7 Days" :value="2"></el-option>
                        <el-option label="Last 30 Days" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select placeholder="" v-model="formData.cashierId">
                        <el-option v-for="item in cashiers" :key="item.id" :value="item.id"
                            :label="item.nickName"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" native-type="submit">Search</el-button>
                    <el-button type="danger" plain @click="reset">Reset</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="report-container">
            <div class="item stake">
                <span class="value">{{ $numeral(reportData.totalStake) }}</span>
                <span class="title">Total Stake</span>
            </div>
            <div class="item win">
                <span class="value">{{ $numeral(reportData.totalPaid) }}</span>
                <span class="title">Total Win</span>
            </div>
            <div class="item profit">
                <span class="value">{{ $numeral(reportData.totalStake - reportData.totalPaid) }}</span>
                <span class="title">Profit</span>
            </div>
        </div>
        <div class="box">
            <div class="panel-header with-border">
                <div class="panel-title">Ticket List</div>
            </div>
            <div class="panel-body" style="padding: 0;" v-loading="state.loading">
                <io-table height="480" class="ticket-table" :pagination="state.pagination" :data="state.rows"
                    @pager-change="onPagerChange">
                    <el-table-column prop="" label="Action" width="145px">
                        <template #default="{ row }">
                            <el-button type="primary" size="small" @click="viewItem(row)">Detail</el-button>
                            <el-button type="info" size="small" @click="onPrint(row)">Print</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderNo" label="Order No" show-overflow-tooltip min-width="80px" />
                    <el-table-column prop="matchName" label="Game Name" />
                    <el-table-column prop="round" label="SN" />
                    <el-table-column prop="stake" label="Stake">
                        <template #default="{ row }">
                            {{ $numeral(row.stake) }}
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="maxPayout" label="MAX PAYOUT">
                        <template #default="{ row }">
                            {{ $numeral(row.maxPayout) }}
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="actualPayout" label="WIN">
                        <template #default="{ row }">
                            {{ $numeral(row.actualPayout) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="Status">
                        <template #default="{ row }">
                            <span v-enum="{ type: EOrderState, value: row.status }"></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="cashier" label="Cashier" />
                    <el-table-column prop="settledBy" label="Settled By" />
                </io-table>
            </div>
        </div>
    </div>

    <ticket-dialog ref="orderDialog" @refresh="getPaginatedList" @reuse="onReuse" />
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import { useResettableReactive } from '@/hooks/useResettableReactive';


import TicketDialog from './ticket-dialog.vue';
import usePrinter from '@/hooks/usePrinter';
import { AccountModel, OrderModel } from '@/api/typings';
import { EOrderState } from '@/typings/enum'
import { formatDate } from '@/utils';
import UserService from '@/api/user';
import { useGlobalStore } from '@/store/global';


const onPagerChange = (pagination: any) => {
    Object.assign(state.pagination, pagination);
    Object.assign(formData, pagination);
    getPaginatedList();
}

//搜索条件
const [formData, resetFormData] = useResettableReactive<{
    pageIndex: number,
    pageSize: number,
    orderNO: string,
    matchId?: number,
    round?: number,
    cashier: string,
    cashierId: string | number;
    startDate?: string;
    dateType?: number;
    endDate?: string;
}>({
    pageIndex: 1,
    pageSize: 10,
    orderNO: '',
    matchId: undefined,
    round: undefined,
    cashier: '',
    cashierId: useGlobalStore().profileModel?.id ?? '',
    dateType: 1,
    startDate: formatDate(new Date(), 'YYYY-MM-DD'),
    endDate: formatDate(new Date(), 'YYYY-MM-DD'),
})

const state = reactive<{
    rows: OrderModel[];
    pagination?: any;
    loading: boolean;
}>({
    rows: [],
    loading: false,
    pagination: {
        pageIndex: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
    },
});

const search = () => {
    formData.pageIndex = 1;
    getPaginatedList();
    getReport();
}

const reportData = ref({
    totalStake: 0,
    totalPayout: 0,
    totalPaid: 0,
    totalUnPaid: 0,
    profit: 0,
    profitRate: 0
})
const getReport = () => {
    OrderService.report(formData).then(res => {
        reportData.value = res.result;
    })
}

const quickDate = (value: number) => {
    //Yesterday
    if (value == 0) {
        formData.startDate = formatDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
        formData.endDate = formData.startDate;
    }
    //Today
    if (value == 1) {
        formData.startDate = formatDate(new Date(), 'YYYY-MM-DD');
        formData.endDate = formatDate(new Date(), 'YYYY-MM-DD');
    }
    //Last 7 Days
    if (value == 2) {
        formData.startDate = formatDate(new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
        formData.endDate = formatDate(new Date(), 'YYYY-MM-DD');
    }
    //Last 30 Days
    if (value == 3) {
        formData.startDate = formatDate(new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
        formData.endDate = formatDate(new Date(), 'YYYY-MM-DD');
    }
    search();
}

const reset = () => {
    resetFormData();
    reportData.value = {
        totalStake: 0,
        totalPayout: 0,
        totalPaid: 0,
        totalUnPaid: 0,
        profit: 0,
        profitRate: 0
    }
    getPaginatedList();
}

const orderDialogRef = useTemplateRef<InstanceType<typeof TicketDialog>>('orderDialog');

const viewItem = (item: OrderModel) => {
    orderDialogRef.value?.open(item.id);
}

const onReuse = (order: OrderModel) => {
    nextTick(() => {
        orderDialogRef.value?.open(order.id);
    });
}

const getPaginatedList = () => {
    state.loading = true;
    const params = {
        ...formData,
        matchId: formData.matchId ? Number(formData.matchId) : undefined,
        round: formData.round ? Number(formData.round) : undefined
    };

    OrderService.getPaginatedList(params).then(res => {
        console.log(res);
        state.rows = res.items;
        state.pagination.pageIndex = res.pageIndex;
        state.pagination.totalCount = res.totalCount;
        state.pagination.totalPages = res.totalPages;
        formData.pageIndex = res.pageIndex;
    }).finally(() => {
        state.loading = false;
    });
}

// 禁用开始日期的逻辑：如果有结束日期，则禁用晚于结束日期的日期
const disableStartDate = (date: Date) => {
    if (formData.endDate) {
        return date > new Date(formData.endDate);
    }
    return false;
}

// 禁用结束日期的逻辑：如果有开始日期，则禁用早于开始日期的日期
const disableEndDate = (date: Date) => {
    if (formData.startDate) {
        return date < new Date(formatDate(formData.startDate, 'YYYY-MM-DD 00:00:00'));
    }
    return false;
}


const onPrint = (order: any) => {
    OrderService.query({ id: order.id }).then(res => {
        usePrinter(res.result);
    });
}

const cashiers = ref<Array<AccountModel>>([]);
const getCashiers = () => {
    UserService.cashiers().then(res => {
        cashiers.value = res.result;
    });
}

onMounted(() => {
    getCashiers();
    getPaginatedList();
})

</script>

<style scoped lang="scss">
.report-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .item {
        margin-right: 20px;
        width: 150px;
        // height: 80px;
        border-radius: 5px;
        font-size: 26px;
        padding: 10px;

        span {
            display: block;

            &.title {
                font-size: 14px;
            }
        }

        &.stake {
            background-color: #CFF4FC;
            color: #0B5665;
            border: 1px solid #ABECF9;
        }

        &.win {
            background-color: #D1E7DD;
            color: #05321D;
            border: 1px solid #B7D9C9;
        }

        &.profit {
            background-color: #FFF3CD;
            color: #705710;
            border: 1px solid #FFEBAF;
        }
    }
}

.search-form {
    display: flex;
    gap: 10px;

    --el-text-color-regular: #000;

    .el-form-item {
        margin-bottom: 0;
    }

    .el-input,
    .el-select,
    .el-date-editor {
        width: 150px;
        --el-select-width: 150px;
    }

    :deep(.el-date-editor.el-input,
        .el-date-editor.el-input__wrapper) {
        --el-date-editor-width: 150px
    }
}

:deep(.ticket-table) {
    --el-table-bg-color: #D3D6E0;
    --el-table-header-text-color: #fff;
    --el-table-header-bg-color: #000;
    --el-table-tr-bg-color: #50688E;
    --el-table-text-color: #fff;
    --el-fill-color-lighter: #465979;
    --el-table-row-hover-bg-color: #748cb3;
    font-size: 16px;
}

.page-body {
    flex-direction: column;
}
</style>
export interface MatchOption {
    id?: number;
    matchId?: number;
    optionId?: number;
    odds: number;
    optionName: string;
    style?: string;
    type?: string;
    createTime?: string;
    stake?: number;
    payout?: number;
}


export interface StakeOrder {
    branchId?: number;
    branchName?: string;
    cashier?: string;
    round?: number | string;
    roundId?: string;
    matchId?: number;
    items: Array<MatchOption>
}
import OrderService from "@/api/order";
import { OrderItem, OrderModel } from "@/api/typings";
import { useGlobalStore } from "@/store/global";
import { formatDate, formatNumber } from "@/utils";


export default function usePrinter(data: OrderModel) {
    if (window.ThermalPrinter) {
        return printInAndroid(data);
    } else {
        return printInBrowser(data);
    }
}

const printInBrowser = (data: OrderModel) => {

    data.title = 'MAGIC ORDER';
    OrderService.print(data).then(res => {
        if (res.code !== 200) {
            ElMessage.error('Print Failed');
        }
    }).catch(err => {
        ElMessageBox.alert(err.message, 'Print Failed');
    });
}


/**
 * 安卓端打印 
 * @param data 
 * @returns 
 */
const printInAndroid = (data: OrderModel) => {

    function formatText(data: OrderModel) {
        let text = '';
        text += `[C]<u><font size='big'>MAGIC ORDER</font></u>\n`;
        text += `[L]\n`;
        text += `[L]<b>Branch: </b>${data.branchName}\n`;
        text += `[L]<b>Cashier: </b>${data.cashier}\n`;
        text += `[L]<b>Barcode: </b>${data.orderNo}\n`;
        // text += `[L]<b>Round: </b>${data.round}\n`;
        text += `[L]<b>Date: </b>${formatDate(data.createTime)}\n`;
        // text += `[L]<b>Valid Until: </b>\n`;
        text += `[C]================================================\n`;

        data.items.map((item: any) => text += formatSelection(item, data));

        text += `[L]<b>Stake</b>[R]<b>TZS ${formatNumber(data.stake)}</b>\n`;
        text += `[L]<b>Max Payout</b>[R]<b>TZS  ${formatNumber(data.maxPayout)}</b>\n`;
        text += `[C]================================================\n`;
        text += `[L]\n`;
        text += `[C]<barcode type='128' height='10'>${data.orderNo}</barcode>\n`;
        text += `[L]\n`;
        text += `[L]Bet responsibly\n`;
        text += `[L]\n`;
        text += `[L]\n`;
        text += `[L]\n`;
        text += `[L]\n`;
        text += `[L]\n`;
        text += `[L]\n`;
        return text;
    }

    function formatSelection(item: OrderItem, data: any) {
        let text = '';
        text += `[L]${data.matchName}[R]Round: ${data.round}\n`;
        text += `[L]<b>WIN: ${item.optionName}(${item.optionType})</b>[R]Odds: ${item.odds}\n`;
        text += `[R]Stake: ${formatNumber(item.stake)}  Payout: ${formatNumber(item.maxPayout)}\n`;
        text += `[C]------------------------------------------------\n`;
        return text;
    }


    const { printerModel } = useGlobalStore();

    if (!printerModel?.id) {
        return Promise.reject('No printer selected');
    }



    const printFormattedText = {
        type: printerModel.type,                  // "bluetooth" | "tcp" | "usb"
        text: formatText(data),             //打印内容
        id: printerModel.id,            //'DC:0D:30:20:61:60',
        printerWidthMM: printerModel.printerWidthMM,                 //纸张宽度 （mm）
        // mmFeedPaper: 120,                //末端毫米距离进纸
        dotsFeedPaper: 50,                   //末端进纸的距离
        printerNbrCharactersPerLine: printerModel.printerNbrCharactersPerLine     //每行字符数 80MM的最大48字符
    }

    return new Promise((resolve, reject) => {
        window.ThermalPrinter.printFormattedTextAndCut(printFormattedText, res => {
            res == 'OK' ? resolve(res) : reject(res);
        }, err => {
            reject(err.error);
        });
    });
}
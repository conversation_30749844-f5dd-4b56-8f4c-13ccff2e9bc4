import { MatchOption } from "@/typings/typings";

const MatchOptions: MatchOption[] = [
    {
        "id": 3001,
        "matchId": 3000,
        "odds": 0,
        "optionName": "1",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3002,
        "matchId": 3000,
        "odds": 0,
        "optionName": "2",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3003,
        "matchId": 3000,
        "odds": 0,
        "optionName": "3",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3004,
        "matchId": 3000,
        "odds": 0,
        "optionName": "4",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3005,
        "matchId": 3000,
        "odds": 0,
        "optionName": "5",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3006,
        "matchId": 3000,
        "odds": 0,
        "optionName": "6",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3007,
        "matchId": 3000,
        "odds": 0,
        "optionName": "7",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3008,
        "matchId": 3000,
        "odds": 0,
        "optionName": "8",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3009,
        "matchId": 3000,
        "odds": 0,
        "optionName": "9",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3010,
        "matchId": 3000,
        "odds": 0,
        "optionName": "10",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3011,
        "matchId": 3000,
        "odds": 0,
        "optionName": "11",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3012,
        "matchId": 3000,
        "odds": 0,
        "optionName": "12",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3013,
        "matchId": 3000,
        "odds": 0,
        "optionName": "13",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3014,
        "matchId": 3000,
        "odds": 0,
        "optionName": "14",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3015,
        "matchId": 3000,
        "odds": 0,
        "optionName": "15",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3016,
        "matchId": 3000,
        "odds": 0,
        "optionName": "16",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3017,
        "matchId": 3000,
        "odds": 0,
        "optionName": "17",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3018,
        "matchId": 3000,
        "odds": 0,
        "optionName": "18",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3019,
        "matchId": 3000,
        "odds": 0,
        "optionName": "19",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3020,
        "matchId": 3000,
        "odds": 0,
        "optionName": "20",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3021,
        "matchId": 3000,
        "odds": 0,
        "optionName": "21",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3022,
        "matchId": 3000,
        "odds": 0,
        "optionName": "22",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3023,
        "matchId": 3000,
        "odds": 0,
        "optionName": "23",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3024,
        "matchId": 3000,
        "odds": 0,
        "optionName": "24",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3025,
        "matchId": 3000,
        "odds": 0,
        "optionName": "25",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3026,
        "matchId": 3000,
        "odds": 0,
        "optionName": "26",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3027,
        "matchId": 3000,
        "odds": 0,
        "optionName": "27",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3028,
        "matchId": 3000,
        "odds": 0,
        "optionName": "28",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3029,
        "matchId": 3000,
        "odds": 0,
        "optionName": "29",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3030,
        "matchId": 3000,
        "odds": 0,
        "optionName": "30",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3031,
        "matchId": 3000,
        "odds": 0,
        "optionName": "31",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3032,
        "matchId": 3000,
        "odds": 0,
        "optionName": "32",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3033,
        "matchId": 3000,
        "odds": 0,
        "optionName": "33",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3034,
        "matchId": 3000,
        "odds": 0,
        "optionName": "34",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3035,
        "matchId": 3000,
        "odds": 0,
        "optionName": "35",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3036,
        "matchId": 3000,
        "odds": 0,
        "optionName": "36",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3037,
        "matchId": 3000,
        "odds": 0,
        "optionName": "37",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3038,
        "matchId": 3000,
        "odds": 0,
        "optionName": "38",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3039,
        "matchId": 3000,
        "odds": 0,
        "optionName": "39",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3040,
        "matchId": 3000,
        "odds": 0,
        "optionName": "40",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3041,
        "matchId": 3000,
        "odds": 0,
        "optionName": "41",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3042,
        "matchId": 3000,
        "odds": 0,
        "optionName": "42",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3043,
        "matchId": 3000,
        "odds": 0,
        "optionName": "43",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3044,
        "matchId": 3000,
        "odds": 0,
        "optionName": "44",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3045,
        "matchId": 3000,
        "odds": 0,
        "optionName": "45",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3046,
        "matchId": 3000,
        "odds": 0,
        "optionName": "46",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3047,
        "matchId": 3000,
        "odds": 0,
        "optionName": "47",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3048,
        "matchId": 3000,
        "odds": 0,
        "optionName": "48",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3049,
        "matchId": 3000,
        "odds": 0,
        "optionName": "49",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3050,
        "matchId": 3000,
        "odds": 0,
        "optionName": "50",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3051,
        "matchId": 3000,
        "odds": 0,
        "optionName": "51",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3052,
        "matchId": 3000,
        "odds": 0,
        "optionName": "52",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3053,
        "matchId": 3000,
        "odds": 0,
        "optionName": "53",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3054,
        "matchId": 3000,
        "odds": 0,
        "optionName": "54",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3055,
        "matchId": 3000,
        "odds": 0,
        "optionName": "55",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3056,
        "matchId": 3000,
        "odds": 0,
        "optionName": "56",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3057,
        "matchId": 3000,
        "odds": 0,
        "optionName": "57",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3058,
        "matchId": 3000,
        "odds": 0,
        "optionName": "58",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3059,
        "matchId": 3000,
        "odds": 0,
        "optionName": "59",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3060,
        "matchId": 3000,
        "odds": 0,
        "optionName": "60",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3061,
        "matchId": 3000,
        "odds": 0,
        "optionName": "61",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3062,
        "matchId": 3000,
        "odds": 0,
        "optionName": "62",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3063,
        "matchId": 3000,
        "odds": 0,
        "optionName": "63",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3064,
        "matchId": 3000,
        "odds": 0,
        "optionName": "64",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3065,
        "matchId": 3000,
        "odds": 0,
        "optionName": "65",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3066,
        "matchId": 3000,
        "odds": 0,
        "optionName": "66",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3067,
        "matchId": 3000,
        "odds": 0,
        "optionName": "67",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3068,
        "matchId": 3000,
        "odds": 0,
        "optionName": "68",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3069,
        "matchId": 3000,
        "odds": 0,
        "optionName": "69",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3070,
        "matchId": 3000,
        "odds": 0,
        "optionName": "70",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3071,
        "matchId": 3000,
        "odds": 0,
        "optionName": "71",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3072,
        "matchId": 3000,
        "odds": 0,
        "optionName": "72",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3073,
        "matchId": 3000,
        "odds": 0,
        "optionName": "73",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3074,
        "matchId": 3000,
        "odds": 0,
        "optionName": "74",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3075,
        "matchId": 3000,
        "odds": 0,
        "optionName": "75",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3076,
        "matchId": 3000,
        "odds": 0,
        "optionName": "76",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3077,
        "matchId": 3000,
        "odds": 0,
        "optionName": "77",
        "type": "NUMBER",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3078,
        "matchId": 3000,
        "odds": 0,
        "optionName": "78",
        "type": "NUMBER",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3079,
        "matchId": 3000,
        "odds": 0,
        "optionName": "79",
        "type": "NUMBER",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3080,
        "matchId": 3000,
        "odds": 0,
        "optionName": "80",
        "type": "NUMBER",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3081,
        "matchId": 3000,
        "odds": 4,
        "optionName": "Blue",
        "type": "COLOR",
        "style": "blue",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3082,
        "matchId": 3000,
        "odds": 4,
        "optionName": "Orange",
        "type": "COLOR",
        "style": "orange",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3083,
        "matchId": 3000,
        "odds": 4,
        "optionName": "Purple",
        "type": "COLOR",
        "style": "purple",
        "createTime": "2025-06-11T23:55:48+08:00"
    },
    {
        "id": 3084,
        "matchId": 3000,
        "odds": 4,
        "optionName": "Green",
        "type": "COLOR",
        "style": "green",
        "createTime": "2025-06-11T23:55:48+08:00"
    }
]
export default MatchOptions;
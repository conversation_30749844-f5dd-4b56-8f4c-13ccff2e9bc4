declare module 'axios' {
    interface AxiosRequestConfig {
        //跳过响应拦截器
        skipResponseInterceptor?: boolean;
        //跳过请求拦截器
        skipRequestInterceptor?: boolean;
    }
}

import { TokenKey } from '@/constants';
import axios, { AxiosError, AxiosResponse, isCancel } from 'axios';

import router from '@/router';
import Cookies from 'js-cookie';

const bizErrorCode = {
    401: (response: AxiosResponse) => {
        ElMessage.error(response.data.message);
        router.replace('/login')
    },
    403: (response: AxiosResponse) => {
        ElMessage.error(response.data.message);
    },
    409: (response: AxiosResponse) => {
        ElMessage.error(response.data.message);
    },
    500: (response: AxiosResponse) => {
        ElMessage.error(response.data.message);
    }
} as const;

const httpErrorCode = {
    401: (_error: AxiosError) => {
        router.replace({ path: '/login' });
    },
    403: (_error: AxiosError) => {
        ElMessage.error('没有权限访问');
    },
    404: (_error: AxiosError) => {
        ElMessage.error('请求的资源不存在');
    },
    500: (_error: AxiosError) => {
        ElMessage.error('服务器内部错误');
    },
    undefined: (error: AxiosError) => {
        ElNotification({
            title: 'Error',
            message: error.message,
            type: 'error',
        })
    }
} as const;

const http = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 60 * 1000,
});

//请求拦截器
http.interceptors.request.use(
    config => {
        if (config.skipRequestInterceptor) {
            return config
        }

        const token = Cookies.get(TokenKey);

        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        console.log("request error", error);
        return Promise.reject(error);
    }
);


//响应拦截器
http.interceptors.response.use(
    response => {
        if (response.config.skipResponseInterceptor) {
            return response.data;
        }

        if (response.data instanceof Blob) {
            return response.data;
        }

        const { code } = response.data;
        if (code === 200) {
            return response.data;
        }

        const errorHandler = bizErrorCode[code as keyof typeof bizErrorCode];
        if (errorHandler) {
            errorHandler(response);
        } else {
            ElMessage.error(response.data.message || '未知业务错误');
        }

        return Promise.reject(response);
    },
    error => {

        if (isCancel(error)) { 
            console.log('Request canceled', error.message);
            return Promise.reject(error);
        }

        const status = error.response?.status;
        const errorHandler = httpErrorCode[status as keyof typeof httpErrorCode] || httpErrorCode.undefined;
        errorHandler(error);

        return Promise.reject(error);
    }
);


export const createCancelTokenSource = () => axios.CancelToken.source();

export default http;

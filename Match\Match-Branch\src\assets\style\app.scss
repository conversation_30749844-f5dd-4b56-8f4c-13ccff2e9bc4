/********** css rest ***************/
html,
body,
#app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 400;
  /* background-color: #f7f7f7; */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1em;
  line-height: 1.42857143;
  min-width: 400px;
  // overflow: hidden;

  .el-message-box {
    --el-messagebox-width: 300px;
  }
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 400;
  font-size: 100%;
  color: inherit;
  margin: 0;
}

.full-width {
  width: 100%;
}

*,
::after,
::after {
  box-sizing: border-box;
}

::selection {
  /* background: #00D1B2;
  color: #fff; */
}

.full-page {
  width: 100%;
  height: 100vh;
}

a {
  background: transparent;
  outline: none;
  transition: color .3s ease;
}


.text-right {
  text-align: right
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}


.panel {
  position: relative;
  background: #ffffff;
  margin-bottom: 5px;
  /* width: 100%; */
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);

  .panel-header {

    color: #444;
    display: block;
    padding: 5px 10px;
    position: relative;
    border-bottom: 1px solid #f1f1f1;

    &.with-border {
      border-bottom: 1px solid #f1f1f1;
    }

    .panel-title {
      display: inline-block;
      font-size: 16px;
      margin: 0;
      line-height: 1;
    }
  }

  .panel-body {
    padding: 5px 10px;
  }

  .panel-footer {
    padding: 5px 10px;
    border-top: 1px solid #f1f1f1;
  }
}


.options {
  display: flex;
  flex-direction: row;

  .item {
    width: 60px;
    height: 30px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    border-radius: 3px;
    background-color: #635d5d;
    font-size: 16px;
    margin-right: 10px;
    cursor: pointer;
    user-select: none;

    &.selected {
      position: relative;
      box-shadow: 0 0 0 2px #fff, 0 0 0 4px #409EFF;
      transform: scale(1.03);
      transition: all 0.2s ease;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      &::after {
        content: '✓';
        position: absolute;
        top: -7px;
        right: -7px;
        background-color: #409EFF;
        color: white;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        font-size: 10px;
        line-height: 15px;
        text-align: center;
      }
    }

    &.green {
      background-color: #1cd41c;
    }

    &.red {
      background-color: #9b1e24;
    }

    &.black {
      background-color: #000000;
    }

    &.gray {
      background-color: #635d5d;
    }
  }
}

.sector,
.exact-number,
.dozens,
.other {}


.exact-number {
  .number {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    height: 148px;
  }

  .zero {
    .item {
      line-height: 148px;
      height: 148px;
    }
  }

  .item {
    width: 40px;
    height: 35px;
    margin-right: 2px;
    margin-bottom: 2px;
    font-size: 16px;
    line-height: 35px;
  }
}

.dozens {
  .item {
    width: 130px;
    margin-right: 10px;
  }
}

.other {
  // margin-top: 5px;

  .item {
    width: 60px;
    margin-right: 10px;
  }
}


.page-body {
  gap: 10px;
}

.text-right {
  text-align: right;
}

.right-panel {
  flex: 1;
  overflow-x: hidden;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.left-panel {
  // max-width: 480px;
}

.box {
  background-color: #D3D6E0;
  border-radius: 5px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  padding: 10px;
  // overflow: auto;

  h3 {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
  }
}

.button {
  border-radius: 8px;
  width: 80px;
  height: 50px;
  color: #fff;
  font-weight: bold;
  // 添加文字描边
  // -webkit-text-stroke: 1px #000;
  // -webkit-text-stroke-color: #000;
  // -webkit-text-stroke-width: 1px;
  user-select: none;
  padding: 0;
  font-size: 24px;
  position: relative;
  border: 1px outset #000;

  &.selected {
    // border-color: #fff;
    // border-color: red;
    // border-width: 5px;
    border: 5px solid yellow;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 3px;
    }
  }

  &.other {
    font-size: 20px;
  }

  &.number {
    font-size: 36px;
  }

  &.green {
    background: linear-gradient(180deg,
        #DFFE8A 0%,
        /* 上半部分起始色 */
        #DCF95F 25%,
        /* 上半部分结束色 */
        #D7FF76 50%,
        /* 中间分界线 */
        #47890c 53%,
        /* 下半部分起始色 */
        #47890c 100%
        /* 下半部分结束色 */
      );
  }

  &.dozens,
  &.sector {
    font-size: 20px;

    span {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      display: block;
    }
  }

  &.red {
    // 上下两段不同渐变
    background: linear-gradient(180deg,
        #ED5125 0%,
        /* 上半部分起始色 */
        #CF2F1A 25%,
        /* 上半部分结束色 */
        #A72C1C 50%,
        /* 中间分界线 */
        #790700 53%,
        /* 下半部分起始色 */
        #883225 100%
        /* 下半部分结束色 */
      );
  }

  &.gray {
    color: #fff;
    // -webkit-text-stroke: 1px #fff;
    // -webkit-text-stroke-color: #fff;
    // -webkit-text-stroke-width: 1px;
    background: linear-gradient(180deg,
        #7E8186 0%,
        /* 上半部分起始色 */
        #7E8186 25%,
        /* 上半部分结束色 */
        #7E8186 50%,
        /* 中间分界线 */
        #424147 53%,
        /* 下半部分起始色 */
        #424147 100%
        /* 下半部分结束色 */
      );

  }

  &.white {
    color: #000;
    // -webkit-text-stroke: 1px #fff;
    // -webkit-text-stroke-color: #fff;
    // -webkit-text-stroke-width: 1px;
    background: linear-gradient(180deg,
        #e7ebef 0%,
        /* 上半部分起始色 */
        #e0e8eb 25%,
        /* 上半部分结束色 */
        #e1e3e8 50%,
        /* 中间分界线 */
        #c0c1da 53%,
        /* 下半部分起始色 */
        #c3cfe5 100%
        /* 下半部分结束色 */
      );

  }

  &.black,
  &.dozens {
    background: linear-gradient(180deg,
        #5d5d55 0%,
        /* 上半部分起始色 */
        #42413c 25%,
        /* 上半部分结束色 */
        #383732 50%,
        /* 中间分界线 */
        #1e1b16 53%,
        /* 下半部分起始色 */
        #1a1711 100%
        /* 下半部分结束色 */
      );
  }


}


[data-tooltip] {
  position: relative;
}

[data-tooltip]:before {
  content: attr(data-tooltip);
  position: absolute;
  color: #fff;
  padding: 5px;
  border-radius: 3px;
  font-size: 26px;
  right: -20px;
  top: 0;
  z-index: 1;
  opacity: 0;
  transform: translateY(5px);
  visibility: hidden;

  // 添加过渡动画
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

[data-tooltip]:hover:before {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.search-form.el-form--inline {
  .el-form-item {
    margin-bottom: 5px;
    width: 200px;
    margin-right: 5px;
  }
}
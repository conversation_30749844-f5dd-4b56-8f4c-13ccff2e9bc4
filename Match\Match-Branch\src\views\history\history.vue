<template>
    <div class="nav-header">
        <span> Hello Cashier</span> <b>(Branch-1)</b>
    </div>
    <div class="page-body flex-col">
        <div class="panel">
            <div class="panel-body">
                <el-form :inline="true" :model="formData" ref="searchForm">
                    <el-form-item label="Bar Code">
                        <el-input placeholder="" v-model="formData.orderNO" />
                    </el-form-item>
                    <el-form-item label="Match">
                        <el-input placeholder="" v-model="formData.matchId" />
                    </el-form-item>
                    <el-form-item label="Round">
                        <el-input placeholder="" v-model="formData.round" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="danger" plain @click="resetFormData">Clear</el-button>
                        <el-button type="primary" @click="search">Search</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">Order List</div>
            </div>
            <div class="panel-body">
                <io-table height="500" :pagination="state.pagination" :data="state.rows" @pager-change="onPagerChange">
                    <el-table-column prop="orderNO" label="BAR CODE" show-overflow-tooltip width="180" />
                    <el-table-column prop="matchName" label="MATCH NAME" />
                    <el-table-column prop="round" label="ROUND" />
                    <el-table-column prop="stake" label="STAKE">
                        <template #default="{ row }">
                            {{ $numeral(row.stake) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="maxPayout" label="MAX PAYOUT">
                        <template #default="{ row }">
                            {{ $numeral(row.maxPayout) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="cashier" label="CASHIER" />
                    <el-table-column prop="" label="ACTION">
                        <template #default="{ row }">
                            <el-button plain type="primary" size="small" @click="viewItem(row)">View</el-button>
                            <el-button plain type="info" size="small" @click="onPrint(row)">Print</el-button>
                        </template>
                    </el-table-column>
                </io-table>
            </div>
        </div>
    </div>

    <OrderDialog ref="orderDialog" />
</template>

<script lang="ts" setup>
import OrderService, { Order } from '@/api/order';
import { useResettableReactive } from '@/hooks/useResettableReactive';


import OrderDialog from './orderDialog.vue';
import usePrinter from '@/hooks/usePrinter';


const onPagerChange = (pagination: any) => {
    Object.assign(state.pagination, pagination);
    Object.assign(formData, pagination);

    getPaginatedList();
}

//搜索条件
const [formData, resetFormData] = useResettableReactive<{
    pageIndex: number,
    pageSize: number,
    orderNO: string,
    matchId?: number,
    round?: number,
    cashier: string,
}>({
    pageIndex: 1,
    pageSize: 15,
    orderNO: '',
    matchId: undefined,
    round: undefined,
    cashier: ''
})

const state = reactive<{
    rows: Order[];
    pagination?: any;
}>({
    rows: [],
    pagination: {
        pageIndex: 1,
        pageSize: 15,
        totalCount: 100,
        totalPages: 0,
    },
});

const search = () => {
    formData.pageIndex = 1;
    getPaginatedList();
}

const orderDialogRef = useTemplateRef<InstanceType<typeof OrderDialog>>('orderDialog');

const viewItem = (item: Order) => {
    orderDialogRef.value?.open(item.id);
}

const getPaginatedList = () => {
    const params = {
        ...formData,
        matchId: formData.matchId ? Number(formData.matchId) : undefined,
        round: formData.round ? Number(formData.round) : undefined
    };
    
    OrderService.getPaginatedList(params).then(res => {
        console.log(res);
        state.rows = res.items;
        state.pagination.pageIndex = res.pageIndex;
        state.pagination.totalCount = res.totalCount;
        state.pagination.totalPages = res.totalPages;
        formData.pageIndex = res.pageIndex;
    })
}


const onPrint = (order: any) => {
    usePrinter(order).then(() => {
        ElMessage({
            message: 'Print Successfully',
            type: 'success',
            plain: true,
        })
    }).catch(error => {
        ElMessageBox({
            title: 'ERROR',
            message: `Print Failed - ${error}`,
            type: 'error'
        })
    });
}

onMounted(() => {
    getPaginatedList();
})

</script>

<style scoped lang="scss">
.page-body {
    max-width: 1200px;
    flex-direction: column;
}
</style>
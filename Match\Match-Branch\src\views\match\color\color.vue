<template>
    <div class="page-body flex-row">
        <div class="left-panel">
            <ticket :matchId="matchId" :items="state.items" :disabled="!isValidDigitCount"
                :selected-option="selectedOption" @clear="clearSelections" :digits="selectedDigits">
            </ticket>
        </div>
        <div class="right-panel">
            <div class="box">
                <h3>Number</h3>
                <div class="options color-numbers">
                    <div class="item" @click="onNumberClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'NUMBER')" v-text="item.optionName"
                        :key="key" :class="[item.style, { 'selected': isDigitSelected(item) }]"></div>
                </div>
                <div class="selected-digits justify-between">
                    <div class="options color-numbers">
                        <div class="item" v-for="(item, key) in selectedDigits" v-text="item.optionName"
                            :class="[item.style, { 'selected': isDigitSelected(item) }]" @click="onNumberClick(item)"
                            :key="key"></div>
                    </div>
                    <button class="button red clear" @click="clearSelections">Clear
                        Selection</button>
                </div>
            </div>
            <div class="box">
                <h3>Color</h3>
                <div class="options">
                    <button class="button" @click="onOptionClick(item)" :data-tooltip="'x' + item.odds"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'COLOR')" v-text="item.optionName"
                        :key="key" :class="[item.style, { 'selected': selectedOption.optionId === item.id }]"></button>
                </div>
            </div>

            <div class="box">
                <h3>Random number selection</h3>
                <div class="options winner">
                    <button class="button white" v-for="item in [3, 4, 5, 6, 7, 8]" @click="onRandomClick(item)">{{ item
                        }} OF</button>
                </div>
            </div>
        </div>
    </div>
</template>



<script setup lang="ts">
import matchOptions from './option';

import Ticket from '../../components/ticket.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';
import { MatchOption, StakeOrder } from '@/typings/typings';
import { EMatch } from '@/typings/enum';
import { getRandomNumbers } from '@/utils';


const matchId = ref(EMatch.ColorLucky);

const [state] = useResettableReactive<StakeOrder>({
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    id: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});




// 存储选择的数字
const selectedDigits = ref<MatchOption[]>([]);

// 点击数字按钮
const onNumberClick = (item: MatchOption) => {
    // 检查是否已经选择了该数字，如果已选择则移除（取消选中）
    if (isDigitSelected(item)) {
        const index = selectedDigits.value.indexOf(item);
        if (index !== -1) {
            selectedDigits.value.splice(index, 1);
        }
        return;
    }

    // 如果已经有8位数字，则不再添加
    if (selectedDigits.value.length >= 8) return;

    // 添加数字到选中列表
    selectedDigits.value.push(item);
    // 清除选中的颜色
    resetSelectedOption();
};

//清除选中的数字和选项
const clearSelections = () => {
    clearSelectedDigits();
    resetSelectedOption();
};


// 判断当前数字数量是否有效（3-8位）
const isValidDigitCount = computed(() => {
    const valid = selectedDigits.value.length >= 3 && selectedDigits.value.length <= 8;
    // 当数字数量有效时，自动更新选项
    if (valid) {
        return false;
    }

    if (selectedOption.id !== 0) {
        return false;
    }

    return true;
});

// 检查数字是否已被选择
const isDigitSelected = (option: MatchOption) => {
    return selectedDigits.value.includes(option);
};

// 清空所有选中的数字
const clearSelectedDigits = () => {
    selectedDigits.value = [];
};

const onOptionClick = (e: MatchOption) => {
    clearSelectedDigits();
    if (selectedOption.id === e.id) {
        resetSelectedOption();
        return;
    }
    selectedOption.id = e.id!;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
};

const onRandomClick = (item: number) => {
    clearSelections();

    getRandomNumbers(item).forEach(x => {
        onNumberClick(matchOptions.find(o => o.optionName == x.toString() && o.type == 'NUMBER')!!);
    });
}

</script>


<style scoped lang="scss">
.box {
    margin-bottom: 5px;
}

.selected-number {
    margin-right: 20px;

    span {
        font-weight: bold;
    }
}

.color-numbers {
    gap: 10px;
    flex-wrap: wrap;
    // width: 850px;

    .item {
        margin-right: 0;
        margin-bottom: 1px;
        width: 45px;
        height: 45px;
        border-radius: 1000px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;

        &.selected::before {
            border-radius: 1000px;
        }
    }
}

.item {
    &.blue {
        background-color: #00B0F0;
    }

    &.orange {
        background-color: #FFC000;
    }

    &.purple {
        background-color: #800080;
    }

    &.green {
        background-color: #008000;
    }
}

.button {

    &.clear {
        font-size: 14px;
        width: 80px;
        height: 45px;
    }

    &.blue {
        background-color: #00B0F0;
        font-size: 20px;
        background: linear-gradient(180deg,
                #100CFA 0%,
                /* 上半部分起始色 */
                #100CFA 25%,
                /* 上半部分结束色 */
                #100CFA 50%,
                /* 中间分界线 */
                #0904A5 53%,
                /* 下半部分起始色 */
                #0904A5 100%
                /* 下半部分结束色 */
            );
    }

    &.orange {
        font-size: 20px;
        background: linear-gradient(180deg,
                #FFC000 0%,
                /* 上半部分起始色 */
                #FFC000 25%,
                /* 上半部分结束色 */
                #FFC000 50%,
                /* 中间分界线 */
                #947002 53%,
                /* 下半部分起始色 */
                #947002 100%
                /* 下半部分结束色 */
            );
    }

    &.purple {
        font-size: 20px;
        background: linear-gradient(180deg,
                #ca06ca 0%,
                /* 上半部分起始色 */
                #ca06ca 25%,
                /* 上半部分结束色 */
                #ca06ca 50%,
                /* 中间分界线 */
                #470147 53%,
                /* 下半部分起始色 */
                #470147 100%
                /* 下半部分结束色 */
            );
    }

    &.green {
        font-size: 20px;
        background: linear-gradient(180deg,
                #73DE1A 0%,
                /* 上半部分起始色 */
                #73DE1A 25%,
                /* 上半部分结束色 */
                #73DE1A 50%,
                /* 中间分界线 */
                #348A0A 53%,
                /* 下半部分起始色 */
                #348A0A 100%
                /* 下半部分结束色 */
            );
    }
}

.options {
    gap: 10px;
    // margin-bottom: 10px;
}

.selected-digits {
    border-top: 2px solid #fff;
    padding-top: 10px;
    margin-top: 10px;
    height: 60px;
}
</style>

<template>
    <div class="page-body">
        <div class="box scan">
            <h3>Please scan the barcode with the scanner when the input is focused</h3>
            <div class="panel-body">
                <div class="justify-between align-center">
                    <el-input ref="input" v-model="barcode" class="scan-input" size="large" @keyup.enter="handleScan">
                    </el-input>
                    <el-button style="height: 60px;" type="primary" size="large" @click="focused">Click to Focused</el-button>

                </div>

                <p class="tips">Please scan the barcode with the scanner when the input is focused</p>
            </div>
        </div>
    </div>
    <ticket-dialog ref="TicketDialogRef"></ticket-dialog>
</template>

<script lang="ts" setup>

import TicketDialog from '../ticket/ticket-dialog.vue';

import OrderService from '@/api/order';

const inputRef = useTemplateRef("input");
onMounted(() => {
    inputRef.value?.focus();
});


const focused = () => {
    inputRef.value?.focus();
}

const barcode = ref('');
const ticketDialogRef = useTemplateRef<InstanceType<typeof TicketDialog>>('TicketDialogRef');
const handleScan = () => {

    if (!barcode.value) {
        return;
    }

    OrderService.getByOrderNo({ orderNo: barcode.value }).then(res => {
        if (res.code == 200) {
            ticketDialogRef.value?.openWithOrder(res.result);
        }
    }).finally(() => {
        barcode.value = '';
    });
};
onMounted(() => {
    // handleScan();
});
</script>

<style lang="scss" scoped>
.scan {
    .scan-input {
        margin: 20px 0;
        font-size: 24px;
        height: 60px;
    }
}
</style>
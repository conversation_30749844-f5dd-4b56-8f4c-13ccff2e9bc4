<template>
    <el-table table-layout="fixed" fit stripe ref="table" v-bind="$attrs" >
        <slot></slot>
    </el-table>
    <el-pagination class="pagination" background :layout="attrs.layout" :current-page="pagination.pageIndex"
        :page-sizes="pagination.pageSizes" :page-size="pagination.pageSize" :total="pagination.totalCount"
        @size-change="onSizeChange" @current-change="onCurrentChange" />
</template>

<script setup lang="ts">
import { ElTable } from 'element-plus';

interface Pagination {
    pageIndex: number;
    pageSize: number;
    totalCount: number;
    pageSizes: number[];
}

const attrs = withDefaults(defineProps<{
    layout?: string
    pagination: Pagination
}>(), {
    layout: 'total, prev, pager, next',
    pagination: () => ({
        pageIndex: 1,
        pageSize: 10,
        totalCount: 0,
        pageSizes: [10, 20, 50, 100]
    })
});

const emits = defineEmits<{
    (e: 'pager-change', payload: { pageIndex: number; pageSize: number }): void;
}>()

const onSizeChange = (pageSize: number) => {
    onPagerChange(attrs.pagination.pageIndex, pageSize);
}

const onCurrentChange = (pageIndex: number) => {
    onPagerChange(pageIndex, attrs.pagination.pageSize);
}

const onPagerChange = (pageIndex: number, pageSize: number) => {
    emits('pager-change', { pageIndex, pageSize });
}


const tableRef = useTemplateRef("table");
defineExpose(new Proxy({}, {
    get(_target, key) {
        return tableRef.value?.[key as keyof InstanceType<typeof ElTable>]
    },
    has(_target, key) {
        return tableRef.value ? key in tableRef.value : false;
    }
}))
</script>

<style scoped lang="scss">
.pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-right: 10px;
}
</style>
import { RouteRecordRaw } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: '',
        redirect: '/dashboard',
        component: () => import('@/views/layout/layout.vue'),
        children: [
            {
                path: 'roulette',
                name: 'roulette',
                component: () => import('@/views/match/roulette/roulette.vue')
            },
            {
                path: 'race',
                name: 'race',
                component: () => import('@/views/match/race/race.vue')
            },
            {
                path: 'color',
                name: 'color',
                component: () => import('@/views/match/color/color.vue')
            },
            {
                path: 'ticket',
                name: 'ticket',
                component: () => import('@/views/ticket/ticket.vue')
            },
            {
                path: 'result',
                name: 'result',
                component: () => import('@/views/result/result.vue')
            },
            {
                path: 'printer',
                name: 'printer',
                component: () => import('@/views/printer/printer.vue')
            }
        ]
    },
    {
        path:'/dashboard',
        name: 'dashboard',
        component: () => import('@/views/dashboard/dashboard.vue')
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/passport/login.vue'),
    },
    // fallback router
    // {
    //     path: '/:w+',
    //     component: () => import('@/views/layout/fallback/404.vue'),
    // }
]
export default routes;

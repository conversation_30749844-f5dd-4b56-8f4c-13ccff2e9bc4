<template>
    <el-dialog v-model="visible" :title="`Order: ${order.orderNo}`" width="900px" align-center draggable @closed="close"
        :close-on-click-modal="false" :close-on-press-escape="false" class="result-dialog" :lock-scroll="false">

        <template #header="{ close, titleId, titleClass }">
            <div class="my-header">
                <h4 :id="titleId" :class="titleClass"><span class="order-no">Order: {{ order.orderNo }} [{{
                    order.matchName }}]</span></h4>
            </div>
        </template>

        <div v-loading="loading" class="container">
            <div class="stamp" v-if="order.status == 40"><span>settled</span></div>
            <div class="result">
                Result:{{ result }}
            </div>
            <div class="ticket-table">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px;" class="text-center">SN</th>
                            <th style="width: 300px;">Bet</th>
                            <th style="width: 150px;">MAX ODDS</th>
                            <th style="width: 125px;">STAKE</th>
                            <th style="width: 125px;">WIN</th>
                            <!-- <th style="width: 50px;">RESULT</th> -->
                        </tr>
                    </thead>
                    <el-scrollbar height="250px">
                        <tbody>
                            <tr v-for="(row, index) in order.items" :key="index">
                                <td style="width: 50px;" class="text-center">{{ index + 1 }}</td>
                                <td style="width: 300px;">
                                    <template v-if="order.matchId == 3000">
                                        <span class="option-name" v-html="getSelectedOption(row)"></span> ({{
                                            row.optionType }})
                                    </template>
                                    <template v-else>
                                        {{ row.optionName }} ({{ row.optionType }})
                                    </template>
                                </td>
                                <td style="width: 150px;">{{ row.odds }}</td>
                                <td style="width: 125px;">{{ $numeral(row.stake) }}</td>
                                <td style="width: 125px;" class="text-right act-payout">{{ $numeral(row.actualPayout) }}
                                </td>
                                <!-- <td>
                                <span v-if="row.status == 0">WAIT</span>
                                <span v-if="row.status == 10">WIN</span>
                                <span v-if="row.status == 20">LOSE</span>
                            </td> -->
                            </tr>
                        </tbody>
                    </el-scrollbar>
                    <tfoot>
                        <tr>
                            <td>SN: {{ order.round }}</td>
                            <td colspan="4" class="text-right">Total Stake: {{ $numeral(order.stake) }} Actual Payout:
                                {{
                                    $numeral(order.actualPayout) }}
                                <span v-if="order.status == 10 || order.status == 11" class="state unsettled">State:
                                    unsettled</span>
                                <span v-if="order.status == 40" class="state settled">State: settled</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Order Time: {{ $moment(order.createTime) }}</td>
                            <td class="text-right">
                                <template v-if="order.status == 30">
                                    Cancel Time: {{ $moment(order.cancelTime) }}
                                </template>
                                <template v-if="order.status == 40">
                                    Settled Time: {{ $moment(order.payTime) }}
                                </template>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <template #footer>
            <div class="footer-buttons">
                <button class="button red" :disabled="loading" @click="cancel" v-if="order.status == 0">Cancel</button>
                <div v-else class="button-placeholder"></div>

                <button class="button green" :disabled="loading" @click="reuse">Reuse</button>
                <button class="button green" :disabled="loading" v-if="order.status == 10 || order.status == 11"
                    @click="settlement">Settlement</button>
                <div v-else class="button-placeholder"></div>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import { OrderItem, OrderModel } from '@/api/typings';
import usePrinter from '@/hooks/usePrinter';
import Dialog from '@/utils/dialog';

const emit = defineEmits(['refresh', 'reuse']);

const order = ref<OrderModel>({
    id: '',
    branchId: 0,
    branchName: '',
    cashier: '',
    orderNo: '',
    round: '',
    matchId: 0,
    matchName: '',
    expireTime: '',
    maxPayout: 0,
    stake: 0,
    status: -1,
    printTime: '',
    createTime: '',
    items: []
});

const loading = ref(false);
const visible = ref(false);

const open = (id: string) => {
    loading.value = true;
    visible.value = true;

    OrderService.query({ id }).then(res => {
        order.value = res.result;
    }).finally(() => {
        loading.value = false;
    });
};
const openWithOrder = (val: OrderModel) => {
    loading.value = false;
    visible.value = true;
    order.value = val;
}

const refresh = () => {
    loading.value = true;
    emit('refresh');
    open(order.value.id);
};

const cancel = () => {
    Dialog.confirm('Are you sure to cancel this order?').then(() => {
        OrderService.cancel({ id: order.value.id }).then(res => {
            refresh();
        });
    });
};

const settlement = () => {
    Dialog.confirm('Are you sure to settlement this order?').then(() => {
        OrderService.cash({ id: order.value.id }).then(res => {
            refresh();
        });
    });
};

const reuse = () => {
    Dialog.confirm('Are you sure to reuse this order?').then(() => {
        OrderService.submit(order.value).then(res => {
            usePrinter(res.result);
            visible.value = false;
            emit('refresh');
            emit('reuse', res.result);
        });
    });
};


const getSelectedOption = (row: OrderItem) => {

    if (row.optionType != 'NUMBER') {
        return row.optionName;
    }

    return row.optionName.split(',').map(item => result.value.split(',').includes(item) ? `<b>${item}</b>` : `${item}`).join(',');
}


const result = computed(() => {
    try {
        const data = JSON.parse(order.value.roundResult || '{}');

        if (order.value.matchId == 1000) {
            return data?.number;
        }
        if (order.value.matchId == 2000) {
            return data?.join(',');
        }
        if (order.value.matchId == 3000) {
            return data?.join(',');
        }
    } catch (ex) {
        return '';
    }
});


const close = () => {
    visible.value = false;
    order.value.status = -1;
    order.value.items.length = 0;
};

defineExpose({
    open,
    openWithOrder
});
</script>
<style lang="scss" scoped>
.order-no,
.result {
    font-size: 22px;
    font-weight: bold;
    color: #000;
}

.ticket-table {
    margin-top: 10px;
    font-size: 20px;

    thead,
    tbody tr,
    tfoot tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        text-align: left;
    }

    table {
        // width: 100%;
        border-collapse: collapse;
        // table-layout: fixed;
        color: #fff;
        background-color: #50688E;
    }

    tbody {

        tr:nth-child(even) {
            background-color: #465979;
        }

        tr:hover {
            background-color: #748CB3;
        }
    }

    thead,
    tfoot {
        background-color: #0D090A;
        color: #fff;
    }

    .text-right {
        text-align: right;
    }

    .act-payout {
        padding-right: 15px;
    }

    th,
    td {
        padding: 5px 3px;
        border-bottom: 1px solid #ebeef5;
        text-align: left;
    }

    th {
        font-weight: bold;
    }
}


.state {
    font-size: bold;

    &.unsettled {
        color: #EAB3FE;
    }

    &.settled {
        color: #2CF886;
    }
}

.container {
    position: relative;
}


.footer-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    .button {
        width: 120px;
        font-size: 20px;
        height: 50px;

        &.green {

            background: linear-gradient(180deg,
                    #73DE1A 0%,
                    /* 上半部分起始色 */
                    #73DE1A 25%,
                    /* 上半部分结束色 */
                    #73DE1A 50%,
                    /* 中间分界线 */
                    #348A0A 53%,
                    /* 下半部分起始色 */
                    #348A0A 100%
                    /* 下半部分结束色 */
                );
        }

    }


    .button-placeholder {
        width: 120px;
        height: 50px; // 与按钮高度保持一致
        visibility: hidden; // 占位但不可见
    }
}

.option-name {
    :deep(b) {
        color: red;
    }
}

.stamp {
    user-select: none;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -40%) rotate(-30deg);

    width: 150px;
    height: 150px;
    color: #DF1227;
    border-radius: 50%;
    position: absolute;

    text-align: center;
    font-size: 45px;
    white-space: nowrap;
    font-weight: bold;
    border: 8px solid #DF1227;
    line-height: 150px;
    z-index: 999999;
    padding: 10px;
    box-sizing: content-box;
    animation: stampEffect 0.6s ease-out;
}

@keyframes stampEffect {
    0% {
        transform: translate(-50%, -40%) rotate(-30deg) scale(1.2);
        opacity: 0;
    }

    50% {
        transform: translate(-50%, -40%) rotate(-30deg) scale(0.8);
        opacity: 0.8;
    }

    100% {
        transform: translate(-50%, -40%) rotate(-30deg) scale(1);
        opacity: 1;
    }
}
</style>
<style>
.result-dialog {
    --el-bg-color: #D3D6E0;
    background-color: #D3D6E0;
}
</style>

<template>
    <div class="page-body flex-row">
        <div class="left-panel">
            <ticket :matchId="matchId" :items="state.items" :selected-option="selectedOption"
                @clear="resetSelectedOption"></ticket>
        </div>
        <div class="right-panel">
            <div class="box number-box">
                <h3>Winner</h3>
                <div class="options winner">
                    <button class="button other black" :data-tooltip="'x' + item.odds" @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Winner')" v-text="item.optionName"
                        :key="key" :class="[item.style, { 'selected': selectedOption.id === item.id }]"></button>
                </div>
            </div>
            <div class="box number-box">
                <h3>1st or 2nd</h3>
                <div class="options winner">
                    <button class="button other red" :data-tooltip="'x' + item.odds" @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == '1st/2nd')" v-text="item.optionName"
                        :key="key" :class="[item.style, { 'selected': selectedOption.id === item.id }]"></button>
                </div>
            </div>
            <div class="box number-box">
                <h3>Forecast</h3>
                <div class="options forecast">
                    <button class="button other gray" :data-tooltip="'x' + item.odds" @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Forecast')" v-text="item.optionName"
                        :key="key" :class="[item.style, { 'selected': selectedOption.id === item.id }]"></button>
                </div>
            </div>

            <div class="box number-box">
                <h3>Other</h3>
                <div class="options other">
                    <button class="button other white" :data-tooltip="'x' + item.odds" @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))"
                        v-text="item.optionName" :key="key"
                        :class="[item.style, { 'selected': selectedOption.id === item.id }]"></button>
                </div>
            </div>

        </div>

    </div>
</template>


<script setup lang="ts">
import matchOptions from './option';

import Ticket from '../../components/ticket.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';
import { MatchOption, StakeOrder } from '@/typings/typings';
import { EMatch } from '@/typings/enum';

const matchId = ref(EMatch.VirtualRace);

const [state] = useResettableReactive<StakeOrder>({
    branchId: 0,
    branchName: 'TEST',
    cashier: '',
    roundId: '',
    round: 0,
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    id: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});

const onOptionClick = (e: MatchOption) => {
    // 如果点击的是已选中的选项，则取消选中
    if (selectedOption.id === e.id) {
        resetSelectedOption();
        return;
    }

    // 否则选中新选项
    selectedOption.id = e.id!;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
}

</script>

<style scoped lang="scss">
.box {
    margin-bottom: 5px;
}

.options {
    gap: 10px 10px;
}

.forecast {
    gap: 3px 10px;
    flex-wrap: wrap;

    .item {
        margin-right: 0 !important;
        margin-bottom: 0;
    }
}
</style>